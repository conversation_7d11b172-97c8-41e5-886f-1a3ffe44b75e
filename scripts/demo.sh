#!/bin/bash

# Demo script for ns-watcher controller
# This script demonstrates how to test the namespace watcher

set -e

echo "🚀 Namespace Watcher Controller Demo"
echo "===================================="

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Function to print colored output
print_status() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

print_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if kubectl is available
if ! command -v kubectl &> /dev/null; then
    print_error "kubectl is not installed or not in PATH"
    exit 1
fi

# Check if we can connect to Kubernetes
if ! kubectl cluster-info &> /dev/null; then
    print_error "Cannot connect to Kubernetes cluster. Please check your kubeconfig."
    exit 1
fi

print_success "Connected to Kubernetes cluster"

# Build the controller
print_status "Building the ns-watcher controller..."
make build

# Option 1: Run locally (out-of-cluster)
echo ""
echo "Choose how to run the demo:"
echo "1) Run locally (out-of-cluster) - Recommended for development"
echo "2) Deploy to Kubernetes cluster"
echo ""
read -p "Enter your choice (1 or 2): " choice

case $choice in
    1)
        print_status "Running controller locally..."
        echo ""
        print_warning "The controller will start and watch for namespace creation events."
        print_warning "In another terminal, create a test namespace to see the logs:"
        print_warning "  kubectl create namespace test-demo-\$(date +%s)"
        echo ""
        print_status "Starting controller (Press Ctrl+C to stop)..."
        ./ns-watcher --log-level=info --log-format=json
        ;;
    2)
        print_status "Deploying to Kubernetes..."
        
        # Build Docker image
        print_status "Building Docker image..."
        make docker-build
        
        # Deploy to cluster
        print_status "Deploying RBAC and controller..."
        make deploy
        
        # Wait for deployment to be ready
        print_status "Waiting for deployment to be ready..."
        kubectl wait --for=condition=available --timeout=60s deployment/ns-watcher
        
        print_success "Controller deployed successfully!"
        
        # Show logs
        print_status "Showing controller logs (Press Ctrl+C to stop)..."
        echo ""
        print_warning "In another terminal, create a test namespace to see the logs:"
        print_warning "  kubectl create namespace test-demo-\$(date +%s)"
        echo ""
        
        # Follow logs
        kubectl logs -f deployment/ns-watcher
        ;;
    *)
        print_error "Invalid choice. Please run the script again and choose 1 or 2."
        exit 1
        ;;
esac
