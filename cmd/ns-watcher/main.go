package main

import (
	"context"
	"flag"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/sirupsen/logrus"
	"k8s.io/client-go/kubernetes"
	"k8s.io/klog/v2"

	"ns-watcher/pkg/config"
	"ns-watcher/pkg/controller"
)

var (
	kubeconfig = flag.String("kubeconfig", "", "Path to kubeconfig file. Leave empty for in-cluster config")
	logLevel   = flag.String("log-level", "info", "Log level (debug, info, warn, error)")
	logFormat  = flag.String("log-format", "json", "Log format (json, text)")
)

func main() {
	flag.Parse()

	// Initialize structured logging
	logger := logrus.New()

	// Set log format
	if *logFormat == "json" {
		logger.SetFormatter(&logrus.JSONFormatter{
			TimestampFormat: time.RFC3339,
		})
	} else {
		logger.SetFormatter(&logrus.TextFormatter{
			FullTimestamp:   true,
			TimestampFormat: time.RFC3339,
		})
	}

	// Set log level
	level, err := logrus.ParseLevel(*logLevel)
	if err != nil {
		logger.WithError(err).Fatal("Invalid log level")
	}
	logger.SetLevel(level)

	// Suppress klog output to reduce noise
	klog.SetOutput(os.Stderr)
	klog.InitFlags(nil)
	flag.Set("v", "0")
	flag.Set("logtostderr", "false")
	flag.Set("alsologtostderr", "false")

	logger.WithFields(logrus.Fields{
		"component": "main",
		"version":   "1.0.0",
	}).Info("Starting pod termination watcher controller")

	// Create Kubernetes client configuration
	cfg, err := config.BuildKubeConfig(*kubeconfig)
	if err != nil {
		logger.WithError(err).Fatal("Failed to build Kubernetes config")
	}

	// Create Kubernetes clientset
	clientset, err := kubernetes.NewForConfig(cfg)
	if err != nil {
		logger.WithError(err).Fatal("Failed to create Kubernetes clientset")
	}

	// Create context for graceful shutdown
	ctx, cancel := context.WithCancel(context.Background())
	defer cancel()

	// Create and start the pod termination controller
	podController := controller.NewPodTerminationController(clientset, logger)

	// Handle shutdown signals
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	// Start controller in a goroutine
	go func() {
		if err := podController.Run(ctx); err != nil {
			logger.WithError(err).Error("Pod termination controller failed")
			cancel()
		}
	}()

	logger.Info("Namespace watcher controller started successfully")

	// Wait for shutdown signal
	<-sigChan
	logger.Info("Received shutdown signal, gracefully shutting down...")

	// Cancel context to stop controller
	cancel()

	// Give some time for graceful shutdown
	time.Sleep(2 * time.Second)
	logger.Info("Namespace watcher controller stopped")
}
