package controller

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/apimachinery/pkg/watch"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/flowcontrol"
	"k8s.io/client-go/util/workqueue"

	"ns-watcher/pkg/config"
)

// PodTerminationController watches for pod termination events
type PodTerminationController struct {
	clientset   kubernetes.Interface
	logger      *logrus.Logger
	informer    cache.SharedIndexInformer
	workqueue   workqueue.RateLimitingInterface
	config      *config.ControllerConfig
	rateLimiter flowcontrol.RateLimiter
}

// PodTerminationEvent represents a pod termination event
type PodTerminationEvent struct {
	Type              string                    `json:"type"`
	PodName           string                    `json:"pod_name"`
	Namespace         string                    `json:"namespace"`
	Timestamp         time.Time                 `json:"timestamp"`
	UID               string                    `json:"uid"`
	NodeName          string                    `json:"node_name,omitempty"`
	Phase             string                    `json:"phase"`
	Reason            string                    `json:"reason,omitempty"`
	Message           string                    `json:"message,omitempty"`
	ContainerStatuses []PodContainerTermination `json:"container_statuses,omitempty"`
	Labels            map[string]string         `json:"labels,omitempty"`
	Annotations       map[string]string         `json:"annotations,omitempty"`
	StartTime         *time.Time                `json:"start_time,omitempty"`
	FinishTime        *time.Time                `json:"finish_time,omitempty"`
}

// PodContainerTermination represents termination details for a single container
type PodContainerTermination struct {
	Name       string    `json:"name"`
	ExitCode   int32     `json:"exit_code"`
	Reason     string    `json:"reason,omitempty"`
	Message    string    `json:"message,omitempty"`
	Signal     int32     `json:"signal,omitempty"`
	StartedAt  time.Time `json:"started_at,omitempty"`
	FinishedAt time.Time `json:"finished_at,omitempty"`
}

// NewPodTerminationController creates a new pod termination controller
func NewPodTerminationController(clientset kubernetes.Interface, logger *logrus.Logger) *PodTerminationController {
	config := config.DefaultControllerConfig()

	// Create rate limiter for API calls
	rateLimiter := flowcontrol.NewTokenBucketRateLimiter(
		config.RateLimitQPS,
		config.RateLimitBurst,
	)

	// Create workqueue with rate limiting
	workqueue := workqueue.NewNamedRateLimitingQueue(
		workqueue.DefaultControllerRateLimiter(),
		"pod-termination-events",
	)

	// Create informer for pod events
	listWatcher := &cache.ListWatch{
		ListFunc: func(options metav1.ListOptions) (runtime.Object, error) {
			// Watch for all pods across all namespaces
			options.FieldSelector = fields.Everything().String()
			return clientset.CoreV1().Pods("").List(context.TODO(), options)
		},
		WatchFunc: func(options metav1.ListOptions) (watch.Interface, error) {
			options.FieldSelector = fields.Everything().String()
			return clientset.CoreV1().Pods("").Watch(context.TODO(), options)
		},
	}

	resyncPeriod, _ := time.ParseDuration(config.ResyncPeriod)
	informer := cache.NewSharedIndexInformer(
		listWatcher,
		&corev1.Pod{},
		resyncPeriod,
		cache.Indexers{},
	)

	controller := &PodTerminationController{
		clientset:   clientset,
		logger:      logger,
		informer:    informer,
		workqueue:   workqueue,
		config:      config,
		rateLimiter: rateLimiter,
	}

	// Add event handlers
	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: func(obj interface{}) {
			// Check if pod is already terminated when first seen
			controller.handlePodEvent(obj)
		},
		UpdateFunc: func(oldObj, newObj interface{}) {
			// Check for pod termination on updates
			controller.handlePodEvent(newObj)
		},
		DeleteFunc: func(obj interface{}) {
			// Pod deletion is also a termination event
			controller.handlePodEvent(obj)
		},
	})

	return controller
}

// handlePodEvent handles pod events and filters for termination events
func (c *PodTerminationController) handlePodEvent(obj interface{}) {
	pod, ok := obj.(*corev1.Pod)
	if !ok {
		c.logger.WithField("component", "controller").Error("Failed to cast object to Pod")
		return
	}

	// Only process pods that are in terminated states
	if !isPodTerminated(pod) {
		return
	}

	// Apply rate limiting
	c.rateLimiter.Accept()

	// Create pod termination event
	event := c.createPodTerminationEvent(pod)

	// Add to workqueue for processing
	c.workqueue.Add(event)
}

// isPodTerminated checks if a pod is in a terminated state
func isPodTerminated(pod *corev1.Pod) bool {
	// Check if pod phase indicates termination
	switch pod.Status.Phase {
	case corev1.PodSucceeded, corev1.PodFailed:
		return true
	case corev1.PodRunning, corev1.PodPending:
		// For running/pending pods, check if all containers are terminated
		for _, containerStatus := range pod.Status.ContainerStatuses {
			if containerStatus.State.Terminated != nil {
				return true
			}
		}
		return false
	default:
		return false
	}
}

// createPodTerminationEvent creates a PodTerminationEvent from a terminated pod
func (c *PodTerminationController) createPodTerminationEvent(pod *corev1.Pod) *PodTerminationEvent {
	event := &PodTerminationEvent{
		Type:      "pod_terminated",
		PodName:   pod.Name,
		Namespace: pod.Namespace,
		Timestamp: time.Now(),
		UID:       string(pod.UID),
		NodeName:  pod.Spec.NodeName,
		Phase:     string(pod.Status.Phase),
		Reason:    pod.Status.Reason,
		Message:   pod.Status.Message,
		Labels:    pod.Labels,
	}

	// Filter annotations to avoid noise
	if len(pod.Annotations) > 0 {
		filteredAnnotations := make(map[string]string)
		for key, value := range pod.Annotations {
			if !isSystemAnnotation(key) {
				filteredAnnotations[key] = value
			}
		}
		if len(filteredAnnotations) > 0 {
			event.Annotations = filteredAnnotations
		}
	}

	// Set start and finish times
	if pod.Status.StartTime != nil {
		event.StartTime = &pod.Status.StartTime.Time
	}

	// Extract container termination details
	var containerStatuses []PodContainerTermination
	for _, containerStatus := range pod.Status.ContainerStatuses {
		if containerStatus.State.Terminated != nil {
			terminated := containerStatus.State.Terminated
			containerTermination := PodContainerTermination{
				Name:       containerStatus.Name,
				ExitCode:   terminated.ExitCode,
				Reason:     terminated.Reason,
				Message:    terminated.Message,
				Signal:     terminated.Signal,
				StartedAt:  terminated.StartedAt.Time,
				FinishedAt: terminated.FinishedAt.Time,
			}
			containerStatuses = append(containerStatuses, containerTermination)

			// Use the latest finish time as the pod finish time
			if event.FinishTime == nil || terminated.FinishedAt.After(*event.FinishTime) {
				event.FinishTime = &terminated.FinishedAt.Time
			}
		}
	}
	event.ContainerStatuses = containerStatuses

	return event
}

// Run starts the controller with graceful reconnection handling
func (c *PodTerminationController) Run(ctx context.Context) error {
	defer c.workqueue.ShutDown()

	c.logger.WithField("component", "controller").Info("Starting pod termination controller")

	// Start informer with retry logic
	return c.runWithRetry(ctx)
}

// runWithRetry implements the main controller loop with automatic reconnection
func (c *PodTerminationController) runWithRetry(ctx context.Context) error {
	maxRetries := 5
	baseDelay := time.Second * 2

	for attempt := 0; attempt < maxRetries; attempt++ {
		if attempt > 0 {
			delay := time.Duration(1<<uint(attempt-1)) * baseDelay
			if delay > time.Minute*5 {
				delay = time.Minute * 5
			}
			c.logger.WithFields(logrus.Fields{
				"component": "controller",
				"attempt":   attempt + 1,
				"delay":     delay.String(),
			}).Warn("Retrying controller startup after failure")

			select {
			case <-time.After(delay):
			case <-ctx.Done():
				return ctx.Err()
			}
		}

		if err := c.runOnce(ctx); err != nil {
			c.logger.WithFields(logrus.Fields{
				"component": "controller",
				"attempt":   attempt + 1,
				"error":     err.Error(),
			}).Error("Controller run failed")

			if attempt == maxRetries-1 {
				return fmt.Errorf("controller failed after %d attempts: %w", maxRetries, err)
			}
			continue
		}

		// If we reach here, the controller ran successfully until context cancellation
		return nil
	}

	return fmt.Errorf("controller failed after %d attempts", maxRetries)
}

// runOnce runs the controller once until context cancellation or error
func (c *PodTerminationController) runOnce(ctx context.Context) error {
	// Start informer
	go c.informer.Run(ctx.Done())

	// Wait for cache to sync with timeout
	syncCtx, cancel := context.WithTimeout(ctx, time.Minute*2)
	defer cancel()

	if !cache.WaitForCacheSync(syncCtx.Done(), c.informer.HasSynced) {
		return fmt.Errorf("failed to wait for cache sync within timeout")
	}

	c.logger.WithField("component", "controller").Info("Cache synced, starting workers")

	// Start workers
	for i := 0; i < c.config.Workers; i++ {
		go wait.UntilWithContext(ctx, c.runWorker, time.Second)
	}

	c.logger.WithField("component", "controller").Info("Pod termination controller started successfully")

	// Wait for context cancellation
	<-ctx.Done()
	c.logger.WithField("component", "controller").Info("Shutting down pod termination controller")

	return nil
}

// runWorker processes items from the workqueue
func (c *PodTerminationController) runWorker(ctx context.Context) {
	for c.processNextWorkItem(ctx) {
	}
}

// processNextWorkItem processes a single item from the workqueue
func (c *PodTerminationController) processNextWorkItem(ctx context.Context) bool {
	obj, shutdown := c.workqueue.Get()
	if shutdown {
		return false
	}

	defer c.workqueue.Done(obj)

	event, ok := obj.(*PodTerminationEvent)
	if !ok {
		c.logger.WithField("component", "worker").Error("Failed to cast workqueue item to PodTerminationEvent")
		c.workqueue.Forget(obj)
		return true
	}

	// Process the pod termination event
	if err := c.processPodTerminationEvent(ctx, event); err != nil {
		// Handle error with exponential backoff
		if c.workqueue.NumRequeues(obj) < 5 {
			c.logger.WithFields(logrus.Fields{
				"component": "worker",
				"pod_name":  event.PodName,
				"namespace": event.Namespace,
				"error":     err.Error(),
				"retries":   c.workqueue.NumRequeues(obj),
			}).Warn("Failed to process pod termination event, retrying")
			c.workqueue.AddRateLimited(obj)
		} else {
			c.logger.WithFields(logrus.Fields{
				"component": "worker",
				"pod_name":  event.PodName,
				"namespace": event.Namespace,
				"error":     err.Error(),
				"retries":   c.workqueue.NumRequeues(obj),
			}).Error("Failed to process pod termination event after max retries, dropping")
			c.workqueue.Forget(obj)
		}
		return true
	}

	// Successfully processed
	c.workqueue.Forget(obj)
	return true
}

// processPodTerminationEvent processes a pod termination event
func (c *PodTerminationController) processPodTerminationEvent(ctx context.Context, event *PodTerminationEvent) error {
	// Log the pod termination event with structured logging
	logFields := logrus.Fields{
		"component":  "pod-termination-watcher",
		"event_type": event.Type,
		"pod_name":   event.PodName,
		"namespace":  event.Namespace,
		"pod_uid":    event.UID,
		"timestamp":  event.Timestamp.Format(time.RFC3339),
		"phase":      event.Phase,
		"level":      "INFO",
	}

	// Add node information if available
	if event.NodeName != "" {
		logFields["node_name"] = event.NodeName
	}

	// Add termination reason and message if available
	if event.Reason != "" {
		logFields["reason"] = event.Reason
	}
	if event.Message != "" {
		logFields["message"] = event.Message
	}

	// Add timing information
	if event.StartTime != nil {
		logFields["start_time"] = event.StartTime.Format(time.RFC3339)
	}
	if event.FinishTime != nil {
		logFields["finish_time"] = event.FinishTime.Format(time.RFC3339)
		if event.StartTime != nil {
			duration := event.FinishTime.Sub(*event.StartTime)
			logFields["duration_seconds"] = duration.Seconds()
		}
	}

	// Add container termination details
	if len(event.ContainerStatuses) > 0 {
		logFields["container_statuses"] = event.ContainerStatuses
	}

	// Add labels if present
	if len(event.Labels) > 0 {
		logFields["labels"] = event.Labels
	}

	// Add annotations if present (filtered to avoid noise)
	if len(event.Annotations) > 0 {
		logFields["annotations"] = event.Annotations
	}

	c.logger.WithFields(logFields).Info("Pod terminated")

	return nil
}

// isSystemAnnotation checks if an annotation is a system annotation
func isSystemAnnotation(key string) bool {
	systemPrefixes := []string{
		"kubectl.kubernetes.io/",
		"kubernetes.io/",
		"k8s.io/",
	}

	for _, prefix := range systemPrefixes {
		if len(key) >= len(prefix) && key[:len(prefix)] == prefix {
			return true
		}
	}
	return false
}
