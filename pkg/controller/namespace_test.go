package controller

import (
	"context"
	"testing"
	"time"

	"github.com/sirupsen/logrus"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/client-go/kubernetes/fake"
	ktesting "k8s.io/client-go/testing"
)

func TestNamespaceController(t *testing.T) {
	// Create a fake clientset
	clientset := fake.NewSimpleClientset()

	// Create a logger for testing
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create the controller
	controller := NewNamespaceController(clientset, logger)

	// Test namespace creation
	testNamespace := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-namespace",
			UID:  "test-uid-123",
			Labels: map[string]string{
				"environment": "test",
			},
			Annotations: map[string]string{
				"description": "Test namespace",
			},
		},
	}

	// Test the handleNamespaceAdd function
	controller.handleNamespaceAdd(testNamespace)

	// Verify that an event was added to the workqueue
	if controller.workqueue.Len() != 1 {
		t.Errorf("Expected 1 item in workqueue, got %d", controller.workqueue.Len())
	}

	// Get the event from the workqueue
	obj, shutdown := controller.workqueue.Get()
	if shutdown {
		t.Error("Workqueue was shut down unexpectedly")
	}

	event, ok := obj.(*NamespaceEvent)
	if !ok {
		t.Error("Expected NamespaceEvent from workqueue")
	}

	// Verify event properties
	if event.Type != "namespace_created" {
		t.Errorf("Expected event type 'namespace_created', got '%s'", event.Type)
	}

	if event.Namespace != "test-namespace" {
		t.Errorf("Expected namespace 'test-namespace', got '%s'", event.Namespace)
	}

	if event.UID != "test-uid-123" {
		t.Errorf("Expected UID 'test-uid-123', got '%s'", event.UID)
	}

	if event.Labels["environment"] != "test" {
		t.Errorf("Expected label 'environment=test', got '%s'", event.Labels["environment"])
	}

	// Test processing the event
	ctx := context.Background()
	err := controller.processNamespaceEvent(ctx, event)
	if err != nil {
		t.Errorf("Failed to process namespace event: %v", err)
	}
}

func TestIsSystemAnnotation(t *testing.T) {
	testCases := []struct {
		annotation string
		expected   bool
	}{
		{"kubectl.kubernetes.io/last-applied-configuration", true},
		{"kubernetes.io/managed-by", true},
		{"k8s.io/some-annotation", true},
		{"custom.example.com/annotation", false},
		{"description", false},
		{"", false},
	}

	for _, tc := range testCases {
		result := isSystemAnnotation(tc.annotation)
		if result != tc.expected {
			t.Errorf("isSystemAnnotation(%s) = %v, expected %v", tc.annotation, result, tc.expected)
		}
	}
}

func TestControllerRun(t *testing.T) {
	// Create a fake clientset
	clientset := fake.NewSimpleClientset()

	// Create a logger for testing
	logger := logrus.New()
	logger.SetLevel(logrus.ErrorLevel) // Reduce log noise in tests

	// Create the controller
	controller := NewNamespaceController(clientset, logger)

	// Create a context with timeout
	ctx, cancel := context.WithTimeout(context.Background(), 2*time.Second)
	defer cancel()

	// Start the controller in a goroutine
	errChan := make(chan error, 1)
	go func() {
		errChan <- controller.Run(ctx)
	}()

	// Wait a bit for the controller to start
	time.Sleep(100 * time.Millisecond)

	// Create a namespace to trigger an event
	testNamespace := &corev1.Namespace{
		ObjectMeta: metav1.ObjectMeta{
			Name: "test-namespace-run",
			UID:  "test-uid-run-123",
		},
	}

	// Add a reactor to simulate namespace creation
	clientset.PrependReactor("create", "namespaces", func(action ktesting.Action) (handled bool, ret runtime.Object, err error) {
		// Trigger the add handler
		controller.handleNamespaceAdd(testNamespace)
		return false, nil, nil
	})

	// Create the namespace
	_, err := clientset.CoreV1().Namespaces().Create(context.TODO(), testNamespace, metav1.CreateOptions{})
	if err != nil {
		t.Errorf("Failed to create test namespace: %v", err)
	}

	// Wait for context to timeout (controller should shut down gracefully)
	select {
	case err := <-errChan:
		if err != nil {
			t.Errorf("Controller returned error: %v", err)
		}
	case <-time.After(3 * time.Second):
		t.Error("Controller did not shut down within timeout")
	}
}
