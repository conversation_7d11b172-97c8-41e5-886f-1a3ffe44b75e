package controller

import (
"context"
"testing"
"time"

"github.com/sirupsen/logrus"
corev1 "k8s.io/api/core/v1"
metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
"k8s.io/client-go/kubernetes/fake"
)

func TestPodTerminationController(t *testing.T) {
	// Create a fake clientset
	clientset := fake.NewSimpleClientset()

	// Create a logger for testing
	logger := logrus.New()
	logger.SetLevel(logrus.DebugLevel)

	// Create the controller
	controller := NewPodTerminationController(clientset, logger)

	// Test pod termination
	testPod := &corev1.Pod{
		ObjectMeta: metav1.ObjectMeta{
			Name:      "test-pod",
			Namespace: "default",
			UID:       "test-pod-uid-123",
			Labels: map[string]string{
				"app": "test-app",
			},
		},
		Spec: corev1.PodSpec{
			NodeName: "test-node",
		},
		Status: corev1.PodStatus{
			Phase:   corev1.PodFailed,
			Reason:  "Error",
			Message: "Container failed",
			StartTime: &metav1.Time{
				Time: time.Now().Add(-5 * time.Minute),
			},
			ContainerStatuses: []corev1.ContainerStatus{
				{
					Name: "main-container",
					State: corev1.ContainerState{
						Terminated: &corev1.ContainerStateTerminated{
							ExitCode:   1,
							Reason:     "Error",
							Message:    "Process exited with status 1",
							StartedAt:  metav1.Time{Time: time.Now().Add(-5 * time.Minute)},
							FinishedAt: metav1.Time{Time: time.Now()},
						},
					},
				},
			},
		},
	}

	// Simulate pod termination event
	controller.handlePodEvent(testPod)

	// Verify that the event was created correctly
	if controller.workqueue.Len() != 1 {
		t.Errorf("Expected 1 item in workqueue, got %d", controller.workqueue.Len())
	}

	// Get the event from the workqueue
	obj, shutdown := controller.workqueue.Get()
	if shutdown {
		t.Error("Workqueue was shut down unexpectedly")
	}
	defer controller.workqueue.Done(obj)

	event, ok := obj.(*PodTerminationEvent)
	if !ok {
		t.Error("Expected PodTerminationEvent from workqueue")
	}

	if event.Type != "pod_terminated" {
		t.Errorf("Expected event type 'pod_terminated', got '%s'", event.Type)
	}

	if event.PodName != "test-pod" {
		t.Errorf("Expected pod name 'test-pod', got '%s'", event.PodName)
	}

	if event.Namespace != "default" {
		t.Errorf("Expected namespace 'default', got '%s'", event.Namespace)
	}

	if event.Phase != "Failed" {
		t.Errorf("Expected phase 'Failed', got '%s'", event.Phase)
	}

	if len(event.ContainerStatuses) != 1 {
		t.Errorf("Expected 1 container status, got %d", len(event.ContainerStatuses))
	}

	if event.ContainerStatuses[0].ExitCode != 1 {
		t.Errorf("Expected exit code 1, got %d", event.ContainerStatuses[0].ExitCode)
	}

	// Test processing the event
	ctx := context.Background()
	err := controller.processPodTerminationEvent(ctx, event)
	if err != nil {
		t.Errorf("Failed to process pod termination event: %v", err)
	}
}

func TestIsPodTerminated(t *testing.T) {
	testCases := []struct {
		name        string
		pod         *corev1.Pod
		shouldMatch bool
	}{
		{
			name: "Failed pod should be terminated",
			pod: &corev1.Pod{
				Status: corev1.PodStatus{
					Phase: corev1.PodFailed,
				},
			},
			shouldMatch: true,
		},
		{
			name: "Succeeded pod should be terminated",
			pod: &corev1.Pod{
				Status: corev1.PodStatus{
					Phase: corev1.PodSucceeded,
				},
			},
			shouldMatch: true,
		},
		{
			name: "Running pod should not be terminated",
			pod: &corev1.Pod{
				Status: corev1.PodStatus{
					Phase: corev1.PodRunning,
				},
			},
			shouldMatch: false,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
result := isPodTerminated(tc.pod)
if result != tc.shouldMatch {
t.Errorf("Expected isPodTerminated to return %v, got %v", tc.shouldMatch, result)
}
})
	}
}
