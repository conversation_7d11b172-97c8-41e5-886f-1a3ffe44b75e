package controller

import (
	"context"
	"fmt"
	"time"

	"github.com/sirupsen/logrus"
	corev1 "k8s.io/api/core/v1"
	metav1 "k8s.io/apimachinery/pkg/apis/meta/v1"
	"k8s.io/apimachinery/pkg/fields"
	"k8s.io/apimachinery/pkg/runtime"
	"k8s.io/apimachinery/pkg/util/wait"
	"k8s.io/apimachinery/pkg/watch"
	"k8s.io/client-go/kubernetes"
	"k8s.io/client-go/tools/cache"
	"k8s.io/client-go/util/flowcontrol"
	"k8s.io/client-go/util/workqueue"

	"ns-watcher/pkg/config"
)

// NamespaceController watches for namespace creation events
type NamespaceController struct {
	clientset   kubernetes.Interface
	logger      *logrus.Logger
	informer    cache.SharedIndexInformer
	workqueue   workqueue.RateLimitingInterface
	config      *config.ControllerConfig
	rateLimiter flowcontrol.RateLimiter
}

// NamespaceEvent represents a namespace creation event
type NamespaceEvent struct {
	Type        string            `json:"type"`
	Namespace   string            `json:"namespace"`
	Timestamp   time.Time         `json:"timestamp"`
	UID         string            `json:"uid"`
	Labels      map[string]string `json:"labels,omitempty"`
	Annotations map[string]string `json:"annotations,omitempty"`
}

// NewNamespaceController creates a new namespace controller
func NewNamespaceController(clientset kubernetes.Interface, logger *logrus.Logger) *NamespaceController {
	config := config.DefaultControllerConfig()

	// Create rate limiter for API calls
	rateLimiter := flowcontrol.NewTokenBucketRateLimiter(
		config.RateLimitQPS,
		config.RateLimitBurst,
	)

	// Create workqueue with rate limiting
	workqueue := workqueue.NewNamedRateLimitingQueue(
		workqueue.DefaultControllerRateLimiter(),
		"namespace-events",
	)

	// Create informer for namespace events
	listWatcher := &cache.ListWatch{
		ListFunc: func(options metav1.ListOptions) (runtime.Object, error) {
			// Only watch for namespace objects
			options.FieldSelector = fields.Everything().String()
			return clientset.CoreV1().Namespaces().List(context.TODO(), options)
		},
		WatchFunc: func(options metav1.ListOptions) (watch.Interface, error) {
			options.FieldSelector = fields.Everything().String()
			return clientset.CoreV1().Namespaces().Watch(context.TODO(), options)
		},
	}

	resyncPeriod, _ := time.ParseDuration(config.ResyncPeriod)
	informer := cache.NewSharedIndexInformer(
		listWatcher,
		&corev1.Namespace{},
		resyncPeriod,
		cache.Indexers{},
	)

	controller := &NamespaceController{
		clientset:   clientset,
		logger:      logger,
		informer:    informer,
		workqueue:   workqueue,
		config:      config,
		rateLimiter: rateLimiter,
	}

	// Add event handlers
	informer.AddEventHandler(cache.ResourceEventHandlerFuncs{
		AddFunc: controller.handleNamespaceAdd,
		UpdateFunc: func(oldObj, newObj interface{}) {
			// We only care about creation events, not updates
		},
		DeleteFunc: func(obj interface{}) {
			// We only care about creation events, not deletions
		},
	})

	return controller
}

// handleNamespaceAdd handles namespace creation events
func (c *NamespaceController) handleNamespaceAdd(obj interface{}) {
	namespace, ok := obj.(*corev1.Namespace)
	if !ok {
		c.logger.WithField("component", "controller").Error("Failed to cast object to Namespace")
		return
	}

	// Apply rate limiting
	c.rateLimiter.Accept()

	// Create namespace event
	event := &NamespaceEvent{
		Type:        "namespace_created",
		Namespace:   namespace.Name,
		Timestamp:   time.Now(),
		UID:         string(namespace.UID),
		Labels:      namespace.Labels,
		Annotations: namespace.Annotations,
	}

	// Add to workqueue for processing
	c.workqueue.Add(event)
}

// Run starts the controller
func (c *NamespaceController) Run(ctx context.Context) error {
	defer c.workqueue.ShutDown()

	c.logger.WithField("component", "controller").Info("Starting namespace controller")

	// Start informer
	go c.informer.Run(ctx.Done())

	// Wait for cache to sync
	if !cache.WaitForCacheSync(ctx.Done(), c.informer.HasSynced) {
		return fmt.Errorf("failed to wait for cache sync")
	}

	c.logger.WithField("component", "controller").Info("Cache synced, starting workers")

	// Start workers
	for i := 0; i < c.config.Workers; i++ {
		go wait.UntilWithContext(ctx, c.runWorker, time.Second)
	}

	c.logger.WithField("component", "controller").Info("Controller started successfully")

	// Wait for context cancellation
	<-ctx.Done()
	c.logger.WithField("component", "controller").Info("Shutting down controller")

	return nil
}

// runWorker processes items from the workqueue
func (c *NamespaceController) runWorker(ctx context.Context) {
	for c.processNextWorkItem(ctx) {
	}
}

// processNextWorkItem processes a single item from the workqueue
func (c *NamespaceController) processNextWorkItem(ctx context.Context) bool {
	obj, shutdown := c.workqueue.Get()
	if shutdown {
		return false
	}

	defer c.workqueue.Done(obj)

	event, ok := obj.(*NamespaceEvent)
	if !ok {
		c.logger.WithField("component", "worker").Error("Failed to cast workqueue item to NamespaceEvent")
		c.workqueue.Forget(obj)
		return true
	}

	// Process the namespace creation event
	if err := c.processNamespaceEvent(ctx, event); err != nil {
		// Handle error with exponential backoff
		if c.workqueue.NumRequeues(obj) < 5 {
			c.logger.WithFields(logrus.Fields{
				"component": "worker",
				"namespace": event.Namespace,
				"error":     err.Error(),
				"retries":   c.workqueue.NumRequeues(obj),
			}).Warn("Failed to process namespace event, retrying")
			c.workqueue.AddRateLimited(obj)
		} else {
			c.logger.WithFields(logrus.Fields{
				"component": "worker",
				"namespace": event.Namespace,
				"error":     err.Error(),
				"retries":   c.workqueue.NumRequeues(obj),
			}).Error("Failed to process namespace event after max retries, dropping")
			c.workqueue.Forget(obj)
		}
		return true
	}

	// Successfully processed
	c.workqueue.Forget(obj)
	return true
}

// processNamespaceEvent processes a namespace creation event
func (c *NamespaceController) processNamespaceEvent(ctx context.Context, event *NamespaceEvent) error {
	// Log the namespace creation event with structured logging
	logFields := logrus.Fields{
		"component":     "namespace-watcher",
		"event_type":    event.Type,
		"namespace":     event.Namespace,
		"namespace_uid": event.UID,
		"timestamp":     event.Timestamp.Format(time.RFC3339),
		"level":         "INFO",
	}

	// Add labels if present
	if len(event.Labels) > 0 {
		logFields["labels"] = event.Labels
	}

	// Add annotations if present (filtered to avoid noise)
	if len(event.Annotations) > 0 {
		filteredAnnotations := make(map[string]string)
		for key, value := range event.Annotations {
			// Only include non-system annotations to reduce log noise
			if !isSystemAnnotation(key) {
				filteredAnnotations[key] = value
			}
		}
		if len(filteredAnnotations) > 0 {
			logFields["annotations"] = filteredAnnotations
		}
	}

	c.logger.WithFields(logFields).Info("Namespace created")

	return nil
}

// isSystemAnnotation checks if an annotation is a system annotation
func isSystemAnnotation(key string) bool {
	systemPrefixes := []string{
		"kubectl.kubernetes.io/",
		"kubernetes.io/",
		"k8s.io/",
	}

	for _, prefix := range systemPrefixes {
		if len(key) >= len(prefix) && key[:len(prefix)] == prefix {
			return true
		}
	}
	return false
}
