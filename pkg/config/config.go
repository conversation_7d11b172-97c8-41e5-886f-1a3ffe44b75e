package config

import (
	"os"
	"path/filepath"

	"k8s.io/client-go/rest"
	"k8s.io/client-go/tools/clientcmd"
	"k8s.io/client-go/util/homedir"
)

// BuildKubeConfig creates a Kubernetes client configuration.
// It supports both in-cluster and out-of-cluster configurations.
func BuildKubeConfig(kubeconfig string) (*rest.Config, error) {
	// If kubeconfig path is provided, use it
	if kubeconfig != "" {
		return clientcmd.BuildConfigFromFlags("", kubeconfig)
	}

	// Try in-cluster config first
	if config, err := rest.InClusterConfig(); err == nil {
		return config, nil
	}

	// Fall back to default kubeconfig location
	if home := homedir.HomeDir(); home != "" {
		defaultKubeconfig := filepath.Join(home, ".kube", "config")
		if _, err := os.Stat(defaultKubeconfig); err == nil {
			return clientcmd.BuildConfigFromFlags("", defaultKubeconfig)
		}
	}

	// If all else fails, try to build from environment
	return clientcmd.BuildConfigFromFlags("", "")
}

// ControllerConfig holds configuration for the namespace controller
type ControllerConfig struct {
	// ResyncPeriod defines how often the controller should resync with the API server
	ResyncPeriod string `json:"resyncPeriod" yaml:"resyncPeriod"`
	
	// Workers defines the number of worker goroutines to process events
	Workers int `json:"workers" yaml:"workers"`
	
	// RateLimitQPS defines the QPS limit for API calls
	RateLimitQPS float32 `json:"rateLimitQPS" yaml:"rateLimitQPS"`
	
	// RateLimitBurst defines the burst limit for API calls
	RateLimitBurst int `json:"rateLimitBurst" yaml:"rateLimitBurst"`
}

// DefaultControllerConfig returns a default configuration for the controller
func DefaultControllerConfig() *ControllerConfig {
	return &ControllerConfig{
		ResyncPeriod:   "30m",
		Workers:        2,
		RateLimitQPS:   10.0,
		RateLimitBurst: 20,
	}
}
