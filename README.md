# Kubernetes Pod Termination Watcher

A Kubernetes controller written in Go that watches for pod termination events and logs detailed information when pods are terminated. The controller monitors pod lifecycle events and captures comprehensive termination details for debugging and monitoring purposes.

## Features

- **Pod Termination Monitoring**: Watches for pod termination events (Failed/Succeeded phases and container terminations)
- **Detailed Termination Logging**: Captures comprehensive termination information including:
  - Pod name, namespace, and UID
  - Termination reason and exit codes
  - Timestamp of termination events
  - Node where the pod was running
  - Container-level termination details
  - Duration calculations
  - Error messages and signals
- **Structured Logging**: Outputs logs in JSON format suitable for log aggregation systems
- **Kubernetes Best Practices**: Implements proper controller patterns including:
  - Informer pattern for efficient API watching
  - Rate limiting and backoff for API calls
  - Context-based cancellation support
  - Graceful reconnection handling for API server connectivity issues
  - Proper error handling and graceful shutdown
- **Flexible Configuration**: Supports both in-cluster and out-of-cluster configurations
- **Security**: Follows security best practices with minimal RBAC permissions

## Quick Start

### Prerequisites

- Go 1.21 or later
- Kubernetes cluster access
- kubectl configured
- Docker (for containerized deployment)

### Local Development

1. **Clone and build**:
   ```bash
   git clone <repository-url>
   cd ns-watcher
   make deps
   make build
   ```

2. **Run locally** (out-of-cluster):
   ```bash
   make run-local
   ```

3. **Run with debug logging**:
   ```bash
   make run-debug
   ```

### Kubernetes Deployment

1. **Build and deploy**:
   ```bash
   make docker-build
   make deploy
   ```

2. **View logs**:
   ```bash
   kubectl logs -f deployment/ns-watcher
   ```

3. **Test by creating a namespace**:
   ```bash
   kubectl create namespace test-namespace
   ```

4. **Clean up**:
   ```bash
   kubectl delete namespace test-namespace
   make undeploy
   ```

## Configuration

### Command Line Options

- `--kubeconfig`: Path to kubeconfig file (leave empty for in-cluster config)
- `--log-level`: Log level (debug, info, warn, error) - default: info
- `--log-format`: Log format (json, text) - default: json

### Environment Variables

When running in Kubernetes, the following environment variables are automatically set:
- `POD_NAME`: Name of the pod
- `POD_NAMESPACE`: Namespace of the pod

## Log Output Format

The controller outputs structured logs in JSON format with comprehensive pod termination details:

```json
{
  "component": "pod-termination-watcher",
  "event_type": "pod_terminated",
  "pod_name": "example-pod-abc123",
  "namespace": "default",
  "pod_uid": "12345678-1234-1234-1234-123456789abc",
  "timestamp": "2024-01-15T10:30:45Z",
  "phase": "Failed",
  "reason": "Error",
  "message": "Container failed with exit code 1",
  "node_name": "worker-node-1",
  "start_time": "2024-01-15T10:25:30Z",
  "finish_time": "2024-01-15T10:30:45Z",
  "duration_seconds": 315.0,
  "container_statuses": [
    {
      "name": "main-container",
      "exit_code": 1,
      "reason": "Error",
      "message": "Process exited with status 1",
      "signal": 0,
      "started_at": "2024-01-15T10:25:30Z",
      "finished_at": "2024-01-15T10:30:45Z"
    }
  ],
  "labels": {
    "app": "example-app",
    "version": "1.0.0"
  },
  "annotations": {
    "deployment.kubernetes.io/revision": "1"
  },
  "level": "info",
  "time": "2024-01-15T10:30:45Z",
  "msg": "Pod terminated"
}
```

## Architecture

### Components

- **Main Controller**: Orchestrates the namespace watching logic
- **Informer**: Efficiently watches Kubernetes API for namespace events
- **Work Queue**: Processes events with rate limiting and retry logic
- **Structured Logger**: Provides consistent, structured logging output

### Rate Limiting

The controller implements rate limiting to prevent API server overload:
- **QPS Limit**: 10 queries per second (configurable)
- **Burst Limit**: 20 concurrent requests (configurable)
- **Exponential Backoff**: Failed requests are retried with exponential backoff

### Error Handling

- **Retry Logic**: Failed events are retried up to 5 times with exponential backoff
- **Graceful Degradation**: Controller continues operating even if individual events fail
- **Context Cancellation**: Proper shutdown handling with context cancellation

## RBAC Permissions

The controller requires minimal RBAC permissions:

```yaml
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["get", "list", "watch"]
```

## Development

### Project Structure

```
ns-watcher/
├── cmd/ns-watcher/          # Main application entry point
├── pkg/
│   ├── controller/          # Controller implementation
│   └── config/             # Configuration utilities
├── deployments/            # Kubernetes manifests
├── Dockerfile             # Container image definition
├── Makefile              # Build and deployment automation
└── README.md            # This file
```

### Building

```bash
# Download dependencies
make deps

# Build binary
make build

# Build Docker image
make docker-build

# Run tests
make test
```

### Testing

Create a test namespace to verify the controller is working:

```bash
kubectl create namespace test-$(date +%s)
```

You should see a log entry similar to:
```json
{
  "component": "namespace-watcher",
  "event_type": "namespace_created",
  "namespace": "test-1642248645",
  "namespace_uid": "...",
  "timestamp": "2024-01-15T10:30:45Z",
  "level": "info",
  "time": "2024-01-15T10:30:45Z",
  "msg": "Namespace created"
}
```

## Troubleshooting

### Common Issues

1. **Permission Denied**: Ensure RBAC is properly configured
2. **Connection Refused**: Check kubeconfig path and cluster connectivity
3. **No Events**: Verify the controller is running and watching the correct API

### Debug Mode

Run with debug logging to see detailed information:
```bash
make run-debug
```

### Logs

Check controller logs:
```bash
kubectl logs -f deployment/ns-watcher
```

## License

[Add your license information here]
