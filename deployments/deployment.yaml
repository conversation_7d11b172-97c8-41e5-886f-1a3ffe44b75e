apiVersion: apps/v1
kind: Deployment
metadata:
  name: pod-termination-watcher
  namespace: default
  labels:
    app: pod-termination-watcher
    version: "1.0.0"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: pod-termination-watcher
  template:
    metadata:
      labels:
        app: pod-termination-watcher
        version: "1.0.0"
    spec:
      serviceAccountName: pod-termination-watcher
      containers:
      - name: pod-termination-watcher
        image: pod-termination-watcher:latest
        imagePullPolicy: IfNotPresent
        args:
        - "--log-level=info"
        - "--log-format=json"
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        resources:
          requests:
            memory: "128Mi"
            cpu: "100m"
          limits:
            memory: "256Mi"
            cpu: "200m"
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep -f pod-termination-watcher"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep -f pod-termination-watcher"
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 65534
          capabilities:
            drop:
            - ALL
      restartPolicy: Always
      securityContext:
        fsGroup: 65534
