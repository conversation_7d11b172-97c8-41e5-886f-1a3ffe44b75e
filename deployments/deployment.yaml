apiVersion: apps/v1
kind: Deployment
metadata:
  name: ns-watcher
  namespace: default
  labels:
    app: ns-watcher
    version: "1.0.0"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ns-watcher
  template:
    metadata:
      labels:
        app: ns-watcher
        version: "1.0.0"
    spec:
      serviceAccountName: ns-watcher
      containers:
      - name: ns-watcher
        image: ns-watcher:latest
        imagePullPolicy: IfNotPresent
        args:
        - "--log-level=info"
        - "--log-format=json"
        env:
        - name: POD_NAME
          valueFrom:
            fieldRef:
              fieldPath: metadata.name
        - name: POD_NAMESPACE
          valueFrom:
            fieldRef:
              fieldPath: metadata.namespace
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        livenessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep -f ns-watcher"
          initialDelaySeconds: 30
          periodSeconds: 30
          timeoutSeconds: 5
          failureThreshold: 3
        readinessProbe:
          exec:
            command:
            - /bin/sh
            - -c
            - "pgrep -f ns-watcher"
          initialDelaySeconds: 5
          periodSeconds: 10
          timeoutSeconds: 5
          failureThreshold: 3
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: true
          runAsNonRoot: true
          runAsUser: 65534
          capabilities:
            drop:
            - ALL
      restartPolicy: Always
      securityContext:
        fsGroup: 65534
