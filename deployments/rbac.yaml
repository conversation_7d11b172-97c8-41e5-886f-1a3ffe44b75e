apiVersion: v1
kind: ServiceAccount
metadata:
  name: ns-watcher
  namespace: default
  labels:
    app: ns-watcher
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: ns-watcher
  labels:
    app: ns-watcher
rules:
- apiGroups: [""]
  resources: ["namespaces"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: ns-watcher
  labels:
    app: ns-watcher
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: ns-watcher
subjects:
- kind: ServiceAccount
  name: ns-watcher
  namespace: default
