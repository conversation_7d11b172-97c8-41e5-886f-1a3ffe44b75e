apiVersion: v1
kind: ServiceAccount
metadata:
  name: pod-termination-watcher
  namespace: default
  labels:
    app: pod-termination-watcher
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRole
metadata:
  name: pod-termination-watcher
  labels:
    app: pod-termination-watcher
rules:
- apiGroups: [""]
  resources: ["pods"]
  verbs: ["get", "list", "watch"]
- apiGroups: [""]
  resources: ["nodes"]
  verbs: ["get", "list"]
- apiGroups: [""]
  resources: ["events"]
  verbs: ["get", "list", "watch"]
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: pod-termination-watcher
  labels:
    app: pod-termination-watcher
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: pod-termination-watcher
subjects:
- kind: ServiceAccount
  name: pod-termination-watcher
  namespace: default
