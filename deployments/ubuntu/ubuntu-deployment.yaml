apiVersion: apps/v1
kind: Deployment
metadata:
  name: ubuntu-test
  namespace: ubuntu
  labels:
    app: ubuntu-test
    version: "latest"
spec:
  replicas: 1
  selector:
    matchLabels:
      app: ubuntu-test
  template:
    metadata:
      labels:
        app: ubuntu-test
        version: "latest"
    spec:
      containers:
      - name: ubuntu
        image: ubuntu:latest
        imagePullPolicy: Always
        command: ["/bin/bash"]
        args: ["-c", "while true; do echo 'Ubuntu container running...'; sleep 30; done"]
        resources:
          requests:
            memory: "64Mi"
            cpu: "50m"
          limits:
            memory: "128Mi"
            cpu: "100m"
        securityContext:
          allowPrivilegeEscalation: false
          readOnlyRootFilesystem: false
          runAsNonRoot: false
          capabilities:
            drop:
            - ALL
      restartPolicy: Always
