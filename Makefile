# Variables
BINARY_NAME=pod-termination-watcher
DOCKER_IMAGE=pod-termination-watcher
DOCKER_TAG=latest
KUBECONFIG?=~/.kube/config

# Go parameters
GOCMD=go
GOBUILD=$(GOCMD) build
GOCLEAN=$(GOCMD) clean
GOTEST=$(GOCMD) test
GOGET=$(GOCMD) get
GOMOD=$(GOCMD) mod

.PHONY: all build clean test deps docker-build docker-run deploy undeploy run-local help

# Default target
all: deps build

# Build the binary
build:
	$(GOBUILD) -o $(BINARY_NAME) -v ./cmd/ns-watcher

# Clean build artifacts
clean:
	$(GOCLEAN)
	rm -f $(BINARY_NAME)

# Run tests
test:
	$(GOTEST) -v ./...

# Download dependencies
deps:
	$(GOMOD) download
	$(GOMOD) tidy

# Build Docker image
docker-build:
	docker build -t $(DOCKER_IMAGE):$(DOCKER_TAG) .

# Run Docker container locally
docker-run: docker-build
	docker run --rm -it \
		-v $(KUBECONFIG):/root/.kube/config:ro \
		$(DOCKER_IMAGE):$(DOCKER_TAG) \
		--kubeconfig=/root/.kube/config \
		--log-level=info \
		--log-format=json

# Deploy to Kubernetes
deploy:
	kubectl apply -f deployments/rbac.yaml
	kubectl apply -f deployments/deployment.yaml

# Remove from Kubernetes
undeploy:
	kubectl delete -f deployments/deployment.yaml --ignore-not-found=true
	kubectl delete -f deployments/rbac.yaml --ignore-not-found=true

# Run locally (out-of-cluster)
run-local: build
	./$(BINARY_NAME) \
		--kubeconfig=$(KUBECONFIG) \
		--log-level=info \
		--log-format=json

# Run locally with debug logging
run-debug: build
	./$(BINARY_NAME) \
		--kubeconfig=$(KUBECONFIG) \
		--log-level=debug \
		--log-format=text

# Load Docker image into kind cluster (for local testing)
kind-load: docker-build
	kind load docker-image $(DOCKER_IMAGE):$(DOCKER_TAG)

# Help
help:
	@echo "Available targets:"
	@echo "  build        - Build the binary"
	@echo "  clean        - Clean build artifacts"
	@echo "  test         - Run tests"
	@echo "  deps         - Download and tidy dependencies"
	@echo "  docker-build - Build Docker image"
	@echo "  docker-run   - Run Docker container locally"
	@echo "  deploy       - Deploy to Kubernetes"
	@echo "  undeploy     - Remove from Kubernetes"
	@echo "  run-local    - Run locally (out-of-cluster)"
	@echo "  run-debug    - Run locally with debug logging"
	@echo "  kind-load    - Load Docker image into kind cluster"
	@echo "  help         - Show this help message"
