# Build stage
FROM golang:1.21-alpine AS builder

# Set working directory
WORKDIR /app

# Install git (needed for go mod download)
RUN apk add --no-cache git

# Copy go mod files
COPY go.mod go.sum ./

# Download dependencies
RUN go mod download

# Copy source code
COPY . .

# Build the application
RUN CGO_ENABLED=0 GOOS=linux go build -a -installsuffix cgo -o ns-watcher ./cmd/ns-watcher

# Final stage
FROM alpine:3.18

# Install ca-certificates for HTTPS calls
RUN apk --no-cache add ca-certificates

# Create non-root user
RUN adduser -D -s /bin/sh -u 65534 appuser

# Set working directory
WORKDIR /root/

# Copy the binary from builder stage
COPY --from=builder /app/ns-watcher .

# Change ownership to non-root user
RUN chown appuser:appuser ns-watcher

# Switch to non-root user
USER appuser

# Expose port (if needed for health checks)
EXPOSE 8080

# Run the binary
ENTRYPOINT ["./ns-watcher"]
